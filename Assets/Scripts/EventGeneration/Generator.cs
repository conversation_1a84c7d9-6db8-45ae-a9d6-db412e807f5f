using System;
using System.Collections.Generic;
using System.Linq;
using Consts;
using Cysharp.Threading.Tasks;
using Drawing;
using Sirenix.OdinInspector;
using Unity.Netcode;
using UnityEngine;
using UnityEngine.Serialization;
using Utils.Debug;
using Utils.Extensions;
using Utils.PrefabManagement;

namespace EventGeneration
{
    public class Generator : InSceneNetworkBehaviour
    {
        #region Debug

        private static DebugLevel DebugLevel => DebugMessengerProvider.GetLevel(type, DebugLevel.Message);
        private static readonly System.Type type = typeof(Generator);

        private List<Tuple<Vector3, Vector3, string, bool>> casts = new(1024);

        #endregion

        #region Settings

        [FormerlySerializedAs("eventDescriptions")] [SerializeField]
        private WeightedRandomList<Event> events;

        [SerializeField] private int numberOfEvents;

        [FormerlySerializedAs("explosionPrefab")] [SerializeField]
        private GameObject decalsCasterPrefab;

        [SerializeField] private LayerMask forceLayerMask;
        [SerializeField] private LayerMask spawnCheckLayermask;
        [SerializeField] private LayerMask wallLayerMask;
        [SerializeReference] private ITagCondition tagCondition;

        #endregion

        #region Privates

        private readonly WeightedRandomList<SpawnGrid> _weightedSpawnGrids = new();

        private readonly List<Vector3> _eventPositions = new();
        private readonly List<CapsuleBounds> _forceCapsules = new();
        private CapsuleBounds _lastCapsuleBounds;

        private readonly Collider[] _overlapResults = new Collider[256];

        // random instance should be created with server seed, never before
        private System.Random _random;

        private readonly List<EventElementData> _serverEventElements = new();
        private readonly List<EventElementData> _clientEventElements = new();

        private readonly HashSet<ulong> _completedClientsIds = new(NumericConsts.MaxPlayers);
        private readonly List<NetworkObject> _spawnedNetworkObjects = new();

        public static readonly List<Generator> ActiveGenerators = new();
        public bool GenerationComplete => _generationComplete;
        private bool _generationComplete;

        private static readonly List<Rigidbody> _rigidbodies = new(16);

        #endregion

        private static readonly List<Quaternion> Rotations = new()
        {
            Quaternion.Euler(0f, 0f, 0f),
            Quaternion.Euler(0f, 90f, 0f),
            Quaternion.Euler(90f, 0f, 0f),
            Quaternion.Euler(90f, 90f, 0f)
        };

        private static readonly List<Vector3> Directions = new()
        {
            new(0, 0, 0),
            new(1f, 0f, 0f),
            new(0, 1, 0),
            new(0f, 0f, 1f),
            new(-1f, 0f, 0f),
            new(0, -1, 0),
            new(0f, 0f, -1f)
        };

        private void OnEnable()
        {
            ActiveGenerators.Add(this);

            _weightedSpawnGrids.Clear();
            _generationComplete = false;

            foreach (var spawnGrid in SpawnGrid.AllSpawnGrids)
            {
                if (!tagCondition.Evaluate(spawnGrid.Tags)) continue;
                
                spawnGrid.Scan();
                _weightedSpawnGrids.Add(spawnGrid, spawnGrid.FreeVoxelsCount);
            }

            if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
                DebugMessenger.Message($"Scanners initialized");
        }

        public void StartGeneration(int seed)
        {
            var isServer = IsServer;
            var isSpawned = IsSpawned;

            if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
                DebugMessenger.Message($"Client[{NetworkManager.LocalClientId}] Start generation [{gameObject.name}] IsSpawned[{isSpawned}] IsServer[{isServer}]");

            if (isServer)
            {
                GenerateClientEventElementsClientRpc(seed);
            }
            else
                _generationComplete = true;
        }

        private void PrepareEventElementData()
        {
            _clientEventElements.Clear();
            _serverEventElements.Clear();

            for (var i = 0; i < numberOfEvents; i++)
            {
                var spawnGrid = _weightedSpawnGrids.GetRandomItem(_random);
                if (spawnGrid == default)
                {
                    if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Error))
                        DebugMessenger.Message($"Could not get random scanner");

                    continue;
                }

                var eventDescriptionIndex = _random.Next(events.Count);
                var eventDescription = events[eventDescriptionIndex];

                var eventPosition = spawnGrid.GetRandomFreeVoxel(_random);
                var eventRotation = Quaternion.Euler(0f, 360f * (float)_random.NextDouble(), 0f);

                foreach (var eventElementSettings in eventDescription.EventElements)
                {
                    var newData = new EventElementData
                    {
                        position = eventPosition + Utils.Extensions.Vector3Extensions.RandomPositionInBox(spawnGrid.voxelSize, _random),
                        rotation = eventRotation,
                        eventElementSettings = eventElementSettings
                    };
                    switch (eventElementSettings)
                    {
                        case DecalEventElementSettings:
                            _clientEventElements.Add(newData);
                            break;
                        default:
                            _serverEventElements.Add(newData);
                            break;
                    }
                }
                
                _clientEventElements.Sort();
            }
        }

        [ClientRpc]
        private void GenerateClientEventElementsClientRpc(int seed)
        {
            if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
                DebugMessenger.Message($"Client [{NetworkManager.LocalClientId}] ({gameObject.name}) generating client event (seed: [{seed}])");

            _random = new System.Random(seed);

            PrepareEventElementData();

            GenerateClient();
        }

        [ServerRpc(RequireOwnership = false)]
        private void ClientGenerationCompletedServerRpc(ulong clientId)
        {
            if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
                DebugMessenger.Message($"Client {clientId} completed event generation");

            _completedClientsIds.Add(clientId);

            if (NetworkManager.Singleton.ConnectedClients.Keys.Any(connectedClientId => !_completedClientsIds.Contains(connectedClientId)))
            {
                if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
                    DebugMessenger.Message($"Not all clients completed event generation, waiting");

                return;
            }

            if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
                DebugMessenger.Message($"All clients completed event generation, generate Server events");

            GenerateServerAsync().Forget();
        }

        private void GenerateClient()
        {
            foreach (var eventData in _clientEventElements)
            {
                var eventElementSettings = eventData.eventElementSettings;

                switch (eventElementSettings)
                {
                    case DecalEventElementSettings:
                        ExplodeDecals(eventData);
                        break;
                }
            }
            
            ClientGenerationCompletedServerRpc(NetworkManager.LocalClientId);
        }

        private async UniTaskVoid GenerateServerAsync()
        {
            foreach (var eventData in _serverEventElements)
            {
                var eventElementSettings = eventData.eventElementSettings;

                switch (eventElementSettings)
                {
                    case PrefabEventElementSettings:
                        SpawnPrefab(eventData);
                        break;
                    case ForceEventElementSettings:
                        ApplyForce(eventData).Forget();
                        break;
                }
                
                await UniTask.WaitForFixedUpdate();
            }
            
            _generationComplete = true;
        }
        #region Event types

        private void ExplodeDecals(EventElementData eventElementData)
        {
            using var decalsCaster = PrefabSpawner.Instance.Spawn(ObjectType.DecalsCaster).GetComponent<DecalsCaster>();

            decalsCaster.transform.SetPositionAndRotation(eventElementData.position, eventElementData.rotation);

            var settings = eventElementData.eventElementSettings as DecalEventElementSettings;
            decalsCaster.settings = settings.SphereCasterSettings;
            decalsCaster.materialSettings = settings.DecalMaterialSettings;

            decalsCaster.Explode(_random);
        }

        private async UniTaskVoid ApplyForce(EventElementData eventElementData)
        {
            await UniTask.WaitForFixedUpdate();
            await UniTask.WaitForFixedUpdate();

            var settings = eventElementData.eventElementSettings as ForceEventElementSettings;
            Physics.Raycast(eventElementData.position, Vector3.up, out var ceilingHitInfo, 3f, wallLayerMask);
            Physics.Raycast(eventElementData.position, Vector3.down, out var floorHitInfo, 3f, wallLayerMask);
            var capsulePosition = eventElementData.position;
            capsulePosition.y = (ceilingHitInfo.point.y + floorHitInfo.point.y) / 2f;
            var interCenterDistance = ceilingHitInfo.point.y - floorHitInfo.point.y;
            var point0 = capsulePosition + Vector3.up * interCenterDistance / 2f;
            var point1 = capsulePosition + Vector3.down * interCenterDistance / 2f;
            var radius = settings.RadiusRange.RandomInRange(_random);
            var capsuleBounds = new CapsuleBounds { point0 = point0, point1 = point1, radius = radius };

            _forceCapsules.Add(capsuleBounds);
            ApplyForce(capsuleBounds, settings.ForceRange);
        }

        [Button]
        private void ApplyForce(CapsuleBounds capsuleBounds, Vector2 forceRange)
        {
            _lastCapsuleBounds = capsuleBounds;
            var itemsCount = Physics.OverlapCapsuleNonAlloc(capsuleBounds.point0, capsuleBounds.point1, capsuleBounds.radius, _overlapResults, forceLayerMask);
            Debug.Log($"Items count: {itemsCount}");

            for (var i = 0; i < itemsCount; i++)
            {
                var itemTransform = _overlapResults[i].transform;

                var direction = Utils.Extensions.Vector3Extensions.RandomDirection(_random);
                direction.y = Mathf.Abs(direction.y);
                var force = direction * forceRange.RandomInRange(_random);

                var interactable = itemTransform.GetComponentInParent<GenericInteractable>();

                if (interactable == null)
                {
                    var rigidBody = _overlapResults[i].attachedRigidbody;
                    if (rigidBody == null)
                        continue;

                    rigidBody.AddForce(force);

                    if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
                        DebugMessenger.Message($"[{itemTransform.gameObject.name}] add force [{force}]");
                }
                else
                {
                    if (interactable.ForceApplied) continue;

                    interactable.GetComponentsInChildren(_rigidbodies);
                    var massSum = _rigidbodies.Sum(rb => rb.mass);
                    var averageAcceleration = force / massSum;
                    foreach (var childRigidBody in _rigidbodies)
                    {
                        var childRigidBodyCenter = childRigidBody.transform.position + childRigidBody.centerOfMass;

                        var hasObstacle = Physics.Linecast(capsuleBounds.Center, childRigidBodyCenter, wallLayerMask);
                        casts.Add(new Tuple<Vector3, Vector3, string, bool>(capsuleBounds.Center, childRigidBodyCenter, childRigidBody.name, hasObstacle));
                        if (hasObstacle)
                            continue;

                        childRigidBody.AddForce(averageAcceleration * Mathf.Pow(childRigidBody.mass, 2f));
                    }

                    interactable.ForceApplied = true;

                    if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
                        DebugMessenger.Message($"[{interactable.gameObject.name}] add force [{force}]");
                }
            }
        }

        private void SpawnPrefab(EventElementData eventElementData)
        {
            var settings = eventElementData.eventElementSettings as PrefabEventElementSettings;

            //var instance = PrefabSpawner.Instance.Spawn()
            // pooling in network environment should be researched
            for (var i = 0; i < settings.Quantity; i++)
                FindFreeSpaceAndSpawn(settings.Prefab, eventElementData.position).Forget();
        }

        private async UniTaskVoid FindFreeSpaceAndSpawn(GameObject prefab, Vector3 position)
        {
            var instance = Instantiate(
                prefab,
                new Vector3(9999f, 9999f, 9999f),
                Quaternion.identity);

            await UniTask.WaitForFixedUpdate();

            var initialBounds = instance.GetCombinedBounds();
            var pivotOffset = instance.transform.position - initialBounds.center;

            initialBounds.center = position;
            var offsetStep = Mathf.Min(Mathf.Min(initialBounds.extents.x, initialBounds.extents.y), initialBounds.extents.z);
            var maxOffset = Mathf.Max(Mathf.Max(initialBounds.extents.x, initialBounds.extents.y), initialBounds.extents.z);

            var steps = maxOffset / offsetStep + 1;
            for (var stepIndex = 1; stepIndex <= steps; stepIndex++)
            {
                foreach (var direction in Directions)
                {
                    var currentBounds = initialBounds;
                    currentBounds.center += direction * offsetStep * stepIndex;
                    var raycastDir = currentBounds.center - position;
                    if (Physics.Raycast(position, raycastDir, raycastDir.magnitude, spawnCheckLayermask))
                        continue;

                    foreach (var rotation in Rotations)
                    {
                        _overlapResults[0] = null;
                        if (Physics.OverlapBoxNonAlloc(currentBounds.center, initialBounds.extents, _overlapResults, rotation, spawnCheckLayermask) != 0)
                            continue;

                        instance.transform.rotation = rotation;
                        instance.transform.position = currentBounds.center + rotation * pivotOffset;
                        _spawnedNetworkObjects.Add(instance.NetworkSpawn());
                        if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
                            DebugMessenger.Message($"Spawned {instance.name} at {instance.transform.position}");
                        return;
                    }
                }
            }

            instance.transform.position = initialBounds.center;
            _spawnedNetworkObjects.Add(instance.NetworkSpawn());
        }

        #endregion

        private void Update()
        {
            foreach (var eventPosition in _eventPositions)
                Draw.WireSphere(eventPosition, 0.25f, Color.yellow);
        
            foreach (var cast in casts)
            {
                Draw.Line(cast.Item1, cast.Item2, cast.Item4 ? Color.red : Color.green);
                Draw.Label2D(cast.Item2, cast.Item3);
            }
                
        
            using (Draw.WithColor(Color.red))
            {
                foreach (var forceCapsule in _forceCapsules)
                {
                    Draw.WireCapsule(forceCapsule.point0, forceCapsule.point1, forceCapsule.radius);
                    Draw.WireSphere(forceCapsule.Center, 0.1f);
                }
            }
            
            using (Draw.WithColor(Color.green))
            {
                Draw.WireCapsule(_lastCapsuleBounds.point0, _lastCapsuleBounds.point1, _lastCapsuleBounds.radius);
                Draw.WireSphere(_lastCapsuleBounds.Center, 0.1f);
            }
        }

        private void OnDisable()
        {
            if (IsServer)
            {
                foreach (var spawnedObject in _spawnedNetworkObjects)
                    spawnedObject.Despawn();

                _spawnedNetworkObjects.Clear();
            }

            ActiveGenerators.Remove(this);
        }

        private struct EventElementData
        {
            public Vector3 position;
            public Quaternion rotation;
            public IEventElementSettings eventElementSettings;
        }

        private enum GenerationType
        {
            Client, Server
        }

#if UNITY_EDITOR
        [Button]
        private void TestCondition()
        {
            var grids = FindObjectsByType<SpawnGrid>(FindObjectsSortMode.None);
            foreach (var grid in grids)
            {
                Debug.Log($"{grid.name}: {tagCondition.Evaluate(grid.Tags)}");
            }
        }
#endif
    }
}